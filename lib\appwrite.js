"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCategories = exports.getMenu = exports.getCurrentUser = exports.signIn = exports.createUser = exports.storage = exports.databases = exports.account = exports.client = exports.appwriteConfig = void 0;
const react_native_appwrite_1 = require("react-native-appwrite");
exports.appwriteConfig = {
    endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT,
    projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID,
    platform: "com.geminointerprices.foodway",
    databaseId: '68927b940012c7765337',
    bucketId: '6893bf4b00244df233b4',
    userCollectionId: '68927c16002839096683',
    categoriesCollectionId: '6893b91a00384571b2fe',
    menuCollectionId: '6893bb5b00029f863d98',
    customizationsCollectionId: '6893bd110021f53b1f04',
    menuCustomizationsCollectionId: '6893be2b003e61892a6f'
};
exports.client = new react_native_appwrite_1.Client();
exports.client
    .setEndpoint(exports.appwriteConfig.endpoint)
    .setProject(exports.appwriteConfig.projectId)
    .setPlatform(exports.appwriteConfig.platform);
exports.account = new react_native_appwrite_1.Account(exports.client);
exports.databases = new react_native_appwrite_1.Databases(exports.client);
exports.storage = new react_native_appwrite_1.Storage(exports.client);
const avatars = new react_native_appwrite_1.Avatars(exports.client);
const createUser = async ({ email, password, name }) => {
    try {
        const newAccount = await exports.account.create(react_native_appwrite_1.ID.unique(), email, password, name);
        if (!newAccount)
            throw Error;
        await (0, exports.signIn)({ email, password });
        const avatarUrl = avatars.getInitialsURL(name);
        return await exports.databases.createDocument(exports.appwriteConfig.databaseId, exports.appwriteConfig.userCollectionId, react_native_appwrite_1.ID.unique(), { email, name, accountId: newAccount.$id, avatar: avatarUrl });
    }
    catch (e) {
        throw new Error(e);
    }
};
exports.createUser = createUser;
const signIn = async ({ email, password }) => {
    try {
        const session = await exports.account.createEmailPasswordSession(email, password);
        return session;
    }
    catch (e) {
        throw new Error(e);
    }
};
exports.signIn = signIn;
const getCurrentUser = async () => {
    try {
        const currentAccount = await exports.account.get();
        if (!currentAccount)
            throw Error;
        const currentUser = await exports.databases.listDocuments(exports.appwriteConfig.databaseId, exports.appwriteConfig.userCollectionId, [react_native_appwrite_1.Query.equal('accountId', currentAccount.$id)]);
        if (!currentUser)
            throw Error;
        return currentUser.documents[0];
    }
    catch (e) {
        console.log(e);
        throw new Error(e);
    }
};
exports.getCurrentUser = getCurrentUser;
const getMenu = async ({ category, query }) => {
    try {
        const queries = [];
        if (category)
            queries.push(react_native_appwrite_1.Query.equal('categories', category));
        if (query)
            queries.push(react_native_appwrite_1.Query.search('name', query));
        const menus = await exports.databases.listDocuments(exports.appwriteConfig.databaseId, exports.appwriteConfig.menuCollectionId, queries);
        return menus.documents;
    }
    catch (e) {
        throw new Error(e);
    }
};
exports.getMenu = getMenu;
const getCategories = async () => {
    try {
        const categories = await exports.databases.listDocuments(exports.appwriteConfig.databaseId, exports.appwriteConfig.categoriesCollectionId);
        return categories.documents;
    }
    catch (e) {
        throw new Error(e);
    }
};
exports.getCategories = getCategories;
