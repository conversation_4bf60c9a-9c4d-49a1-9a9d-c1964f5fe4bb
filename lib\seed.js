"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_native_appwrite_1 = require("react-native-appwrite");
const appwrite_1 = require("./appwrite");
const data_1 = __importDefault(require("./data"));
// ensure dummyData has correct shape
const data = data_1.default;
async function clearAll(collectionId) {
    const list = await appwrite_1.databases.listDocuments(appwrite_1.appwriteConfig.databaseId, collectionId);
    await Promise.all(list.documents.map((doc) => appwrite_1.databases.deleteDocument(appwrite_1.appwriteConfig.databaseId, collectionId, doc.$id)));
}
async function clearStorage() {
    const list = await appwrite_1.storage.listFiles(appwrite_1.appwriteConfig.bucketId);
    await Promise.all(list.files.map((file) => appwrite_1.storage.deleteFile(appwrite_1.appwriteConfig.bucketId, file.$id)));
}
async function uploadImageToStorage(imageUrl) {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    const fileObj = {
        name: imageUrl.split("/").pop() || `file-${Date.now()}.jpg`,
        type: blob.type,
        size: blob.size,
        uri: imageUrl,
    };
    const file = await appwrite_1.storage.createFile(appwrite_1.appwriteConfig.bucketId, react_native_appwrite_1.ID.unique(), fileObj);
    return appwrite_1.storage.getFileViewURL(appwrite_1.appwriteConfig.bucketId, file.$id);
}
async function seed() {
    // 1. Clear all
    await clearAll(appwrite_1.appwriteConfig.categoriesCollectionId);
    await clearAll(appwrite_1.appwriteConfig.customizationsCollectionId);
    await clearAll(appwrite_1.appwriteConfig.menuCollectionId);
    await clearAll(appwrite_1.appwriteConfig.menuCustomizationsCollectionId);
    await clearStorage();
    // 2. Create Categories
    const categoryMap = {};
    for (const cat of data.categories) {
        const doc = await appwrite_1.databases.createDocument(appwrite_1.appwriteConfig.databaseId, appwrite_1.appwriteConfig.categoriesCollectionId, react_native_appwrite_1.ID.unique(), cat);
        categoryMap[cat.name] = doc.$id;
    }
    // 3. Create Customizations
    const customizationMap = {};
    for (const cus of data.customizations) {
        const doc = await appwrite_1.databases.createDocument(appwrite_1.appwriteConfig.databaseId, appwrite_1.appwriteConfig.customizationsCollectionId, react_native_appwrite_1.ID.unique(), {
            name: cus.name,
            price: cus.price,
            type: cus.type,
        });
        customizationMap[cus.name] = doc.$id;
    }
    // 4. Create Menu Items
    const menuMap = {};
    for (const item of data.menu) {
        const uploadedImage = await uploadImageToStorage(item.image_url);
        const doc = await appwrite_1.databases.createDocument(appwrite_1.appwriteConfig.databaseId, appwrite_1.appwriteConfig.menuCollectionId, react_native_appwrite_1.ID.unique(), {
            name: item.name,
            description: item.description,
            image_url: uploadedImage,
            price: item.price,
            rating: item.rating,
            calories: item.calories,
            protein: item.protein,
            categories: categoryMap[item.category_name],
        });
        menuMap[item.name] = doc.$id;
        // 5. Create menu_customizations
        for (const cusName of item.customizations) {
            await appwrite_1.databases.createDocument(appwrite_1.appwriteConfig.databaseId, appwrite_1.appwriteConfig.menuCustomizationsCollectionId, react_native_appwrite_1.ID.unique(), {
                menu: doc.$id,
                customizations: customizationMap[cusName],
            });
        }
    }
    console.log("✅ Seeding complete.");
}
exports.default = seed;
// Auto-run the seed function if this file is executed directly
if (require.main === module) {
    console.log("🌱 Starting database seeding...");
    console.log("📊 This will populate your database with categories, menu items, and customizations");
    console.log("⚠️  WARNING: This will clear existing data!");
    console.log("");
    seed()
        .then(() => {
        console.log("");
        console.log("🎉 Database seeding completed successfully!");
        console.log("📱 You can now start your app and see the seeded data");
        process.exit(0);
    })
        .catch((error) => {
        console.error("");
        console.error("❌ Seeding failed:", error);
        console.error("Check your Appwrite configuration and try again");
        process.exit(1);
    });
}
