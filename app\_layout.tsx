import {SplashScreen, Stack} from "expo-router";
import { useFonts } from 'expo-font';
import { useEffect} from "react";

import './globals.css';
import * as Sentry from '@sentry/react-native';
import useAuthStore from "@/store/auth.store";

Sentry.init({
  dsn: 'https://<EMAIL>/4509588544094208',

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // Configure Session Replay
  replaysSessionSampleRate: 1,
  replaysOnErrorSampleRate: 1,
  integrations: [Sentry.mobileReplayIntegration(), Sentry.feedbackIntegration()],

  // Show feedback widget when errors are sent to Sentry
  beforeSend(event) {
    // Only show feedback widget for actual errors (not performance events)
    if (event.exception || event.level === 'error') {
      console.log('Sentry error captured, showing feedback widget');
      // Small delay to ensure the error is processed first
      setTimeout(() => {
        Sentry.showFeedbackWidget();
      }, 100);
    }
    return event;
  },

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

export default Sentry.wrap(function RootLayout() {
  const { isLoading, fetchAuthenticatedUser } = useAuthStore();

  const [fontsLoaded, error] = useFonts({
    "QuickSand-Bold": require('../assets/fonts/Quicksand-Bold.ttf'),
    "QuickSand-Medium": require('../assets/fonts/Quicksand-Medium.ttf'),
    "QuickSand-Regular": require('../assets/fonts/Quicksand-Regular.ttf'),
    "QuickSand-SemiBold": require('../assets/fonts/Quicksand-SemiBold.ttf'),
    "QuickSand-Light": require('../assets/fonts/Quicksand-Light.ttf'),
  });

  useEffect(() => {
    if(error) throw error;
    if(fontsLoaded) SplashScreen.hideAsync();
  }, [fontsLoaded, error]);

  useEffect(() => {
    fetchAuthenticatedUser()
  }, [fetchAuthenticatedUser]);

  useEffect(() => {
    // Set up global error handler to show feedback widget on errors
    const originalHandler = ErrorUtils.getGlobalHandler();

    ErrorUtils.setGlobalHandler((error, isFatal) => {
      // Call original handler first
      originalHandler(error, isFatal);

      // Show feedback widget when an error occurs
      if (fontsLoaded && !isLoading) {
        console.log('Error occurred, showing Sentry feedback widget');
        Sentry.showFeedbackWidget();
      }
    });

    // Cleanup function to restore original handler
    return () => {
      ErrorUtils.setGlobalHandler(originalHandler);
    };
  }, [fontsLoaded, isLoading]);

  if(!fontsLoaded || isLoading) return null;

  return <Stack screenOptions={{ headerShown: false }} />;
});
