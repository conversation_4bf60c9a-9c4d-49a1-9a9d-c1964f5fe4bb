
        import seed from './lib/seed.js';
        
        console.log('🌱 Starting to seed the database...');
        console.log('📊 This will populate your Appwrite database with:');
        console.log('   - 6 food categories (Burgers, Pizzas, Burritos, etc.)');
        console.log('   - 10 customization options (toppings and sides)');
        console.log('   - 14 menu items with images and details');
        console.log('');
        
        seed()
            .then(() => {
                console.log('\n🎉 Database seeding completed successfully!');
                console.log('\n📱 You can now:');
                console.log('   1. Start your app: npm start');
                console.log('   2. Browse the seeded menu items');
                console.log('   3. Test the search and filter functionality');
                console.log('   4. Add items to cart and test the full flow');
                process.exit(0);
            })
            .catch((error) => {
                console.error('\n❌ Database seeding failed:', error);
                console.log('\n🔧 Troubleshooting:');
                console.log('   1. Check your .env file has correct Appwrite credentials');
                console.log('   2. Verify your Appwrite project is active');
                console.log('   3. Ensure collections exist in your database');
                console.log('   4. Check network connectivity to Appwrite');
                process.exit(1);
            });
    